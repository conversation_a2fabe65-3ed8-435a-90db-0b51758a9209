"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ChevronLeft,
  DollarSign,
  Calendar,
  CreditCard,
  Download,
  Mail,
  Phone,
  CheckCircle,
  Clock,
  AlertCircle,
} from "lucide-react";

// Mock data - replace with actual API calls
const mockMemberData = {
  userId: "u3",
  userName: "<PERSON>",
  email: "<EMAIL>",
  phone: "(*************",
  avatar: "/images/avatars/carol.jpg",
  totalPaid: 1420,
  totalCommitted: 1600,
  activeGroups: 2,
  completedGroups: 3,
  joinedAt: new Date("2023-08-15"),
};

const mockPaymentHistory = [
  {
    id: "pay1",
    groupId: "g1",
    groupName: "Memorial for John Smith",
    amount: 400,
    shareAmount: 400,
    date: new Date("2024-01-20"),
    method: "Credit Card",
    status: "completed",
    transactionId: "txn_1234567890",
  },
  {
    id: "pay2",
    groupId: "g2",
    groupName: "Family Heritage Stone",
    amount: 200,
    shareAmount: 400,
    date: new Date("2024-01-15"),
    method: "Credit Card",
    status: "completed",
    transactionId: "txn_0987654321",
  },
  {
    id: "pay3",
    groupId: "g2",
    groupName: "Family Heritage Stone",
    amount: 120,
    shareAmount: 400,
    date: new Date("2024-01-22"),
    method: "Bank Transfer",
    status: "completed",
    transactionId: "txn_1122334455",
  },
  {
    id: "pay4",
    groupId: "g3",
    groupName: "Custom Memorial Design",
    amount: 300,
    shareAmount: 400,
    date: new Date("2024-02-01"),
    method: "Credit Card",
    status: "completed",
    transactionId: "txn_5566778899",
  },
  {
    id: "pay5",
    groupId: "g4",
    groupName: "Premium Granite Memorial",
    amount: 400,
    shareAmount: 800,
    date: new Date("2024-02-05"),
    method: "Credit Card",
    status: "completed",
    transactionId: "txn_9988776655",
  },
];

const mockGroupMemberships = [
  {
    groupId: "g1",
    groupName: "Memorial for John Smith",
    shareAmount: 400,
    paidAmount: 400,
    status: "completed",
    paymentStatus: "completed",
    joinedAt: new Date("2024-01-10"),
    lastPayment: new Date("2024-01-20"),
  },
  {
    groupId: "g2",
    groupName: "Family Heritage Stone",
    shareAmount: 400,
    paidAmount: 320,
    status: "manufacturing",
    paymentStatus: "partial",
    joinedAt: new Date("2024-01-12"),
    lastPayment: new Date("2024-01-22"),
  },
  {
    groupId: "g3",
    groupName: "Custom Memorial Design",
    shareAmount: 400,
    paidAmount: 300,
    status: "collecting",
    paymentStatus: "partial",
    joinedAt: new Date("2024-01-25"),
    lastPayment: new Date("2024-02-01"),
  },
  {
    groupId: "g4",
    groupName: "Premium Granite Memorial",
    shareAmount: 800,
    paidAmount: 400,
    status: "collecting",
    paymentStatus: "partial",
    joinedAt: new Date("2024-02-01"),
    lastPayment: new Date("2024-02-05"),
  },
];

export default function MemberPaymentHistory() {
  const params = useParams();
  const userId = params.userId as string;

  const [memberData, setMemberData] = useState(mockMemberData);
  const [paymentHistory, setPaymentHistory] = useState(mockPaymentHistory);
  const [groupMemberships, setGroupMemberships] =
    useState(mockGroupMemberships);

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600";
      case "partial":
        return "text-yellow-600";
      case "pending":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "partial":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case "pending":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getGroupStatusColor = (status: string) => {
    switch (status) {
      case "collecting":
        return "bg-blue-100 text-blue-800";
      case "manufacturing":
        return "bg-yellow-100 text-yellow-800";
      case "installing":
        return "bg-purple-100 text-purple-800";
      case "completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/merchant/payments">
          <Button variant="outline" size="icon">
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </Link>
        <div className="flex items-center space-x-4">
          <Avatar className="h-12 w-12">
            <AvatarImage src={memberData.avatar} alt={memberData.userName} />
            <AvatarFallback>
              {memberData.userName
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {memberData.userName}
            </h1>
            <p className="text-muted-foreground">
              Payment history and group memberships
            </p>
          </div>
        </div>
      </div>

      {/* Member Summary */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Paid</CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${memberData.totalPaid.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              of ${memberData.totalCommitted.toLocaleString()} committed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Groups</CardTitle>
            <CheckCircle className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{memberData.activeGroups}</div>
            <p className="text-xs text-muted-foreground">
              Currently participating
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Completed Groups
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {memberData.completedGroups}
            </div>
            <p className="text-xs text-muted-foreground">
              Successfully completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Member Since</CardTitle>
            <Calendar className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {memberData.joinedAt.toLocaleDateString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {Math.floor(
                (new Date().getTime() - memberData.joinedAt.getTime()) /
                  (1000 * 60 * 60 * 24 * 30)
              )}{" "}
              months
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex space-x-6">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{memberData.email}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{memberData.phone}</span>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Mail className="mr-2 h-4 w-4" />
                Email
              </Button>
              <Button variant="outline" size="sm">
                <Phone className="mr-2 h-4 w-4" />
                Call
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="payments" className="space-y-4">
        <TabsList>
          <TabsTrigger value="payments">Payment History</TabsTrigger>
          <TabsTrigger value="groups">Group Memberships</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Payment History</CardTitle>
                  <CardDescription>
                    All payments made by {memberData.userName}
                  </CardDescription>
                </div>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentHistory.map((payment) => (
                  <div
                    key={payment.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">{payment.groupName}</h4>
                        <Badge className="bg-green-100 text-green-800">
                          {payment.status}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{payment.date.toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <CreditCard className="h-3 w-3" />
                          <span>{payment.method}</span>
                        </div>
                        <span>ID: {payment.transactionId}</span>
                      </div>
                    </div>

                    <div className="text-right">
                      <p className="font-medium">
                        ${payment.amount.toLocaleString()}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        of ${payment.shareAmount.toLocaleString()} share
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="groups" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Group Memberships</CardTitle>
              <CardDescription>
                Groups that {memberData.userName} is participating in
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {groupMemberships.map((membership) => (
                  <div
                    key={membership.groupId}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="space-y-2">
                      <div className="flex items-center space-x-3">
                        <h4 className="font-medium">{membership.groupName}</h4>
                        <Badge
                          className={getGroupStatusColor(membership.status)}
                        >
                          {membership.status}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span>
                          Joined:{" "}
                          {membership.joinedAt.toLocaleDateString("en-US")}
                        </span>
                        <span>
                          Last payment:{" "}
                          {membership.lastPayment.toLocaleDateString("en-US")}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-right">
                        <p className="font-medium">
                          ${membership.paidAmount.toLocaleString()} / $
                          {membership.shareAmount.toLocaleString()}
                        </p>
                        <div className="flex items-center space-x-2">
                          {getPaymentStatusIcon(membership.paymentStatus)}
                          <span
                            className={`text-sm ${getPaymentStatusColor(
                              membership.paymentStatus
                            )}`}
                          >
                            {membership.paymentStatus}
                          </span>
                        </div>
                      </div>

                      <Link href={`/merchant/groups/${membership.groupId}`}>
                        <Button variant="outline" size="sm">
                          View Group
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment Analytics</CardTitle>
              <CardDescription>
                Payment patterns and insights for {memberData.userName}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold">
                    $
                    {Math.round(
                      memberData.totalPaid / paymentHistory.length
                    ).toLocaleString()}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Average Payment
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">
                    {Math.round(
                      (memberData.totalPaid / memberData.totalCommitted) * 100
                    )}
                    %
                  </p>
                  <p className="text-sm text-muted-foreground">Payment Rate</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">
                    {
                      paymentHistory.filter((p) => p.method === "Credit Card")
                        .length
                    }
                  </p>
                  <p className="text-sm text-muted-foreground">Card Payments</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">
                    {
                      paymentHistory.filter((p) => p.method === "Bank Transfer")
                        .length
                    }
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Bank Transfers
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
