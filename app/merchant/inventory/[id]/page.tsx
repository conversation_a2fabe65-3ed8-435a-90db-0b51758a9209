"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft, Save, Settings, Package, Eye, Edit } from "lucide-react";
import { MerchantInventoryItem } from "@/types/merchant";
import { getMerchantCategories } from "@/data/merchant";

// Mock data - replace with actual API calls
const mockProduct: MerchantInventoryItem & { applyGlobalPhases?: boolean } = {
  id: "inv1",
  merchantId: "m1",
  name: "Classic Granite Memorial Stone",
  description:
    "Traditional granite memorial stone with elegant design and premium finish. Perfect for honoring loved ones with timeless beauty and durability.",
  price: 1299.99,
  images: ["/images/stone-1.jpg", "/images/stone-2.jpg"],
  category: "traditional",
  specifications: {
    Material: "Premium Granite",
    Dimensions: '24" x 12" x 4"',
    Finish: "Polished",
    Engraving: "Laser Etched",
    Color: "Charcoal Gray",
    "Weather Resistant": "Yes",
  },
  manufacturingTime: 21,
  manufacturingPhases: [],
  isActive: true,
  stockStatus: "made_to_order",
  applyGlobalPhases: true,
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-15"),
};

export default function EditProduct() {
  const params = useParams();
  const productId = params.id as string;

  const [product, setProduct] = useState<MerchantInventoryItem>(mockProduct);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(mockProduct);

  // Get categories for this merchant
  const categories = getMerchantCategories("m1");

  const stockStatuses = [
    { value: "in_stock", label: "In Stock" },
    { value: "made_to_order", label: "Made to Order" },
    { value: "out_of_stock", label: "Out of Stock" },
  ];

  const handleInputChange = (
    field: string,
    value: string | number | boolean
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSave = () => {
    // TODO: Implement actual product update
    setProduct(formData);
    setIsEditing(false);
    console.log("Saving product:", formData);
  };

  const handleCancel = () => {
    setFormData(product);
    setIsEditing(false);
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case "in_stock":
        return "bg-green-100 text-green-800";
      case "made_to_order":
        return "bg-blue-100 text-blue-800";
      case "out_of_stock":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/merchant/inventory">
            <Button variant="outline" size="icon">
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {product.name}
            </h1>
            <p className="text-muted-foreground">
              Product details and settings
            </p>
          </div>
        </div>

        <div className="flex space-x-2">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </Button>
            </>
          ) : (
            <>
              <Link href={`/merchant/inventory/${productId}/manufacturing`}>
                <Button variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  Manufacturing Phases
                </Button>
              </Link>
              <Button onClick={() => setIsEditing(true)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Product
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Status Badges */}
      <div className="flex space-x-2">
        <Badge variant={product.isActive ? "default" : "secondary"}>
          {product.isActive ? "Active" : "Inactive"}
        </Badge>
        <Badge className={getStockStatusColor(product.stockStatus)}>
          {stockStatuses.find((s) => s.value === product.stockStatus)?.label}
        </Badge>
        <Badge variant="outline" className="capitalize">
          {product.category}
        </Badge>
      </div>

      <Tabs defaultValue="details" className="space-y-4">
        <TabsList>
          <TabsTrigger value="details">Product Details</TabsTrigger>
          <TabsTrigger value="images">Images</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-2">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Product Name</Label>
                  {isEditing ? (
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) =>
                        handleInputChange("name", e.target.value)
                      }
                    />
                  ) : (
                    <p className="text-sm">{product.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  {isEditing ? (
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) =>
                        handleInputChange("description", e.target.value)
                      }
                      rows={4}
                    />
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      {product.description}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Price</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={formData.price}
                        onChange={(e) =>
                          handleInputChange("price", parseFloat(e.target.value))
                        }
                      />
                    ) : (
                      <p className="text-sm font-medium">
                        ${product.price.toLocaleString()}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Manufacturing Time</Label>
                    {isEditing ? (
                      <Input
                        type="number"
                        value={formData.manufacturingTime}
                        onChange={(e) =>
                          handleInputChange(
                            "manufacturingTime",
                            parseInt(e.target.value)
                          )
                        }
                      />
                    ) : (
                      <p className="text-sm">
                        {product.manufacturingTime} days
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Category</Label>
                    {isEditing ? (
                      <Select
                        value={formData.category}
                        onValueChange={(value) =>
                          handleInputChange("category", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.slug}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-sm capitalize">{product.category}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Stock Status</Label>
                    {isEditing ? (
                      <Select
                        value={formData.stockStatus}
                        onValueChange={(value) =>
                          handleInputChange("stockStatus", value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {stockStatuses.map((status) => (
                            <SelectItem key={status.value} value={status.value}>
                              {status.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <p className="text-sm">
                        {
                          stockStatuses.find(
                            (s) => s.value === product.stockStatus
                          )?.label
                        }
                      </p>
                    )}
                  </div>
                </div>

                {/* Manufacturing Options */}
                {isEditing && (
                  <div className="space-y-4 pt-4 border-t">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="applyGlobalPhases"
                        checked={formData.applyGlobalPhases || false}
                        onCheckedChange={(checked) =>
                          handleInputChange("applyGlobalPhases", checked)
                        }
                      />
                      <Label
                        htmlFor="applyGlobalPhases"
                        className="text-sm font-medium"
                      >
                        Apply global manufacturing phases to this product
                      </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      When enabled, this product will automatically inherit all
                      global manufacturing phases defined in your merchant
                      settings.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Specifications */}
            <Card>
              <CardHeader>
                <CardTitle>Specifications</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(product.specifications).map(
                    ([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="text-sm font-medium">{key}:</span>
                        <span className="text-sm text-muted-foreground">
                          {value}
                        </span>
                      </div>
                    )
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Product Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Product Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold">12</p>
                  <p className="text-sm text-muted-foreground">Total Orders</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">8</p>
                  <p className="text-sm text-muted-foreground">Active Groups</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">$15,599</p>
                  <p className="text-sm text-muted-foreground">Total Revenue</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">4.9</p>
                  <p className="text-sm text-muted-foreground">Avg Rating</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="images" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Product Images</CardTitle>
              <CardDescription>
                Manage product images and gallery
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {product.images.map((image, index) => (
                  <div
                    key={index}
                    className="aspect-square relative overflow-hidden rounded-lg border"
                  >
                    <img
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      className="object-cover w-full h-full"
                    />
                    {index === 0 && (
                      <Badge className="absolute top-2 left-2">
                        Main Image
                      </Badge>
                    )}
                  </div>
                ))}
                <div className="aspect-square border-2 border-dashed border-muted-foreground/25 rounded-lg flex items-center justify-center">
                  <Button variant="outline">
                    <Package className="mr-2 h-4 w-4" />
                    Add Image
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Product Analytics</CardTitle>
              <CardDescription>
                Performance metrics and insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Analytics dashboard coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
